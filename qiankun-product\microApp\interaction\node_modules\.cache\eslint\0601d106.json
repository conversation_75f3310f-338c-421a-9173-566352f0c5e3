[{"D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagementDetails.vue": "1", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\router\\index.js": "2", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue": "3", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\PhoneVerifyDialog.vue": "4", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\api\\index.js": "5", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\utils\\shareUtils.js": "6", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveShare.vue": "7", "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\ShareDialog.vue": "8"}, {"size": 15344, "mtime": 1756863380916, "results": "9", "hashOfConfig": "10"}, {"size": 8685, "mtime": 1756885820444, "results": "11", "hashOfConfig": "10"}, {"size": 35402, "mtime": 1756954989925, "results": "12", "hashOfConfig": "10"}, {"size": 7096, "mtime": 1756949563767, "results": "13", "hashOfConfig": "10"}, {"size": 12209, "mtime": 1756953397398, "results": "14", "hashOfConfig": "10"}, {"size": 2745, "mtime": 1756949958253, "results": "15", "hashOfConfig": "10"}, {"size": 2278, "mtime": 1756950000311, "results": "16", "hashOfConfig": "10"}, {"size": 4465, "mtime": 1756950145210, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bf5quz", {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagementDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\router\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\PhoneVerifyDialog.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\api\\index.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\utils\\shareUtils.js", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveShare.vue", [], "D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\ShareDialog.vue", []]